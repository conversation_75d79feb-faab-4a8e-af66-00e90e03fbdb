2025-05-30 17:06:04,049 - INFO - Loaded 5 live feed events from file
2025-05-30 17:06:06,289 - INFO - Portable Chromium available: C:\Users\<USER>\PycharmProjects\sorceriosetup\portable_chromium\browser\chrome-win\chrome.exe
2025-05-30 17:06:06,291 - INFO - Creating Chrome driver for test_user (attempt 1/3)
2025-05-30 17:06:07,291 - INFO - Attempting webdriver-manager approach for test_user
2025-05-30 17:06:08,703 - INFO - Portable Chromium available: C:\Users\<USER>\PycharmProjects\sorceriosetup\portable_chromium\browser\chrome-win\chrome.exe
2025-05-30 17:06:08,703 - WARNING - webdriver-manager approach failed: 'ChromeDriverManager' object has no attribute 'install'
2025-05-30 17:06:08,704 - INFO - Attempting portable ChromeDriver approach for test_user
2025-05-30 17:06:10,514 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:06:15,629 - WARNING - Portable ChromeDriver approach failed: Message: session not created: cannot connect to chrome at 127.0.0.1:52922
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0xaef173+62931]
	GetHandleVerifier [0x0xaef1b4+62996]
	(No symbol) [0x0x901053]
	(No symbol) [0x0x93ad1b]
	(No symbol) [0x0x939d39]
	(No symbol) [0x0x93043f]
	(No symbol) [0x0x930276]
	(No symbol) [0x0x97a37a]
	(No symbol) [0x0x979c1a]
	(No symbol) [0x0x96e2a6]
	(No symbol) [0x0x93d5f0]
	(No symbol) [0x0x93e464]
	GetHandleVerifier [0x0xd43463+2504899]
	GetHandleVerifier [0x0xd3e892+2485490]
	GetHandleVerifier [0x0xb1590a+220522]
	GetHandleVerifier [0x0xb06388+157672]
	GetHandleVerifier [0x0xb0cb6d+184269]
	GetHandleVerifier [0x0xaf7158+95672]
	GetHandleVerifier [0x0xaf7300+96096]
	GetHandleVerifier [0x0xae21aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:06:15,631 - INFO - Attempting undetected-chromedriver auto approach for test_user
2025-05-30 17:06:17,376 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:06:18,719 - WARNING - undetected-chromedriver auto approach failed: you cannot reuse the ChromeOptions object
2025-05-30 17:06:18,719 - ERROR - Chrome driver creation failed for test_user (attempt 1): you cannot reuse the ChromeOptions object
2025-05-30 17:06:23,071 - INFO - Creating Chrome driver for test_user (attempt 2/3)
2025-05-30 17:06:24,072 - INFO - Attempting webdriver-manager approach for test_user
2025-05-30 17:06:25,448 - INFO - Portable Chromium available: C:\Users\<USER>\PycharmProjects\sorceriosetup\portable_chromium\browser\chrome-win\chrome.exe
2025-05-30 17:06:25,448 - WARNING - webdriver-manager approach failed: 'ChromeDriverManager' object has no attribute 'install'
2025-05-30 17:06:25,448 - INFO - Attempting portable ChromeDriver approach for test_user
2025-05-30 17:06:27,264 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:06:28,601 - WARNING - Portable ChromeDriver approach failed: you cannot reuse the ChromeOptions object
2025-05-30 17:06:28,602 - INFO - Attempting undetected-chromedriver auto approach for test_user
2025-05-30 17:06:30,375 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:06:31,704 - WARNING - undetected-chromedriver auto approach failed: you cannot reuse the ChromeOptions object
2025-05-30 17:06:31,704 - ERROR - Chrome driver creation failed for test_user (attempt 2): you cannot reuse the ChromeOptions object
2025-05-30 17:06:35,246 - INFO - Creating Chrome driver for test_user (attempt 3/3)
2025-05-30 17:06:36,248 - INFO - Attempting webdriver-manager approach for test_user
2025-05-30 17:06:37,620 - INFO - Portable Chromium available: C:\Users\<USER>\PycharmProjects\sorceriosetup\portable_chromium\browser\chrome-win\chrome.exe
2025-05-30 17:06:37,621 - WARNING - webdriver-manager approach failed: 'ChromeDriverManager' object has no attribute 'install'
2025-05-30 17:06:37,621 - INFO - Attempting portable ChromeDriver approach for test_user
2025-05-30 17:06:39,392 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:06:40,721 - WARNING - Portable ChromeDriver approach failed: you cannot reuse the ChromeOptions object
2025-05-30 17:06:40,721 - INFO - Attempting undetected-chromedriver auto approach for test_user
2025-05-30 17:06:42,478 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:06:43,831 - WARNING - undetected-chromedriver auto approach failed: you cannot reuse the ChromeOptions object
2025-05-30 17:06:43,832 - ERROR - Chrome driver creation failed for test_user (attempt 3): you cannot reuse the ChromeOptions object
2025-05-30 17:06:43,832 - INFO - Trying final fallback with Chrome for Testing for test_user
2025-05-30 17:06:43,832 - ERROR - All Chrome driver creation attempts failed for test_user: ChromeDriverManager.__init__() got an unexpected keyword argument 'chrome_type'
2025-05-30 17:06:43,833 - ERROR - ChromeDriver test failed: ChromeDriverManager.__init__() got an unexpected keyword argument 'chrome_type'
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\sorceriosetup\chrome_driver_manager.py", line 124, in create_driver
    service = Service(ChromeDriverManager().install())
AttributeError: 'ChromeDriverManager' object has no attribute 'install'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\sorceriosetup\chrome_driver_manager.py", line 151, in create_driver
    raise uc_error
  File "C:\Users\<USER>\PycharmProjects\sorceriosetup\chrome_driver_manager.py", line 146, in create_driver
    driver = uc.Chrome(options=options, version_main=None)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\undetected_chromedriver\__init__.py", line 269, in __init__
    raise RuntimeError("you cannot reuse the ChromeOptions object")
RuntimeError: you cannot reuse the ChromeOptions object

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\sorceriosetup\test_chrome_driver.py", line 46, in test_chrome_driver_creation
    driver = create_chrome_driver_for_account(
  File "C:\Users\<USER>\PycharmProjects\sorceriosetup\chrome_driver_manager.py", line 200, in create_chrome_driver_for_account
    return chrome_manager.create_driver(
  File "C:\Users\<USER>\PycharmProjects\sorceriosetup\chrome_driver_manager.py", line 175, in create_driver
    raise e3
  File "C:\Users\<USER>\PycharmProjects\sorceriosetup\chrome_driver_manager.py", line 169, in create_driver
    service = Service(ChromeDriverManager(chrome_type=ChromeType.CHROMIUM).install())
TypeError: ChromeDriverManager.__init__() got an unexpected keyword argument 'chrome_type'
2025-05-30 17:06:43,860 - INFO - Creating Chrome driver for test_user_0 (attempt 1/3)
2025-05-30 17:06:44,862 - INFO - Attempting webdriver-manager approach for test_user_0
2025-05-30 17:06:47,101 - INFO - Portable Chromium available: C:\Users\<USER>\PycharmProjects\sorceriosetup\portable_chromium\browser\chrome-win\chrome.exe
2025-05-30 17:06:47,101 - WARNING - webdriver-manager approach failed: 'ChromeDriverManager' object has no attribute 'install'
2025-05-30 17:06:47,101 - INFO - Attempting portable ChromeDriver approach for test_user_0
2025-05-30 17:06:48,925 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:06:53,447 - WARNING - Portable ChromeDriver approach failed: Message: session not created: cannot connect to chrome at 127.0.0.1:52967
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0xd1f173+62931]
	GetHandleVerifier [0x0xd1f1b4+62996]
	(No symbol) [0x0xb31053]
	(No symbol) [0x0xb6ad1b]
	(No symbol) [0x0xb69d39]
	(No symbol) [0x0xb6043f]
	(No symbol) [0x0xb60276]
	(No symbol) [0x0xbaa37a]
	(No symbol) [0x0xba9c1a]
	(No symbol) [0x0xb9e2a6]
	(No symbol) [0x0xb6d5f0]
	(No symbol) [0x0xb6e464]
	GetHandleVerifier [0x0xf73463+2504899]
	GetHandleVerifier [0x0xf6e892+2485490]
	GetHandleVerifier [0x0xd4590a+220522]
	GetHandleVerifier [0x0xd36388+157672]
	GetHandleVerifier [0x0xd3cb6d+184269]
	GetHandleVerifier [0x0xd27158+95672]
	GetHandleVerifier [0x0xd27300+96096]
	GetHandleVerifier [0x0xd121aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:06:53,449 - INFO - Attempting undetected-chromedriver auto approach for test_user_0
2025-05-30 17:06:55,368 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:06:56,707 - WARNING - undetected-chromedriver auto approach failed: you cannot reuse the ChromeOptions object
2025-05-30 17:06:56,717 - ERROR - Chrome driver creation failed for test_user_0 (attempt 1): you cannot reuse the ChromeOptions object
2025-05-30 17:06:59,952 - INFO - Creating Chrome driver for test_user_0 (attempt 2/3)
2025-05-30 17:07:00,955 - INFO - Attempting webdriver-manager approach for test_user_0
2025-05-30 17:07:02,294 - INFO - Portable Chromium available: C:\Users\<USER>\PycharmProjects\sorceriosetup\portable_chromium\browser\chrome-win\chrome.exe
2025-05-30 17:07:02,295 - WARNING - webdriver-manager approach failed: 'ChromeDriverManager' object has no attribute 'install'
2025-05-30 17:07:02,295 - INFO - Attempting portable ChromeDriver approach for test_user_0
2025-05-30 17:07:04,054 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:07:05,401 - WARNING - Portable ChromeDriver approach failed: you cannot reuse the ChromeOptions object
2025-05-30 17:07:05,402 - INFO - Attempting undetected-chromedriver auto approach for test_user_0
2025-05-30 17:07:07,152 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:07:08,493 - WARNING - undetected-chromedriver auto approach failed: you cannot reuse the ChromeOptions object
2025-05-30 17:07:08,493 - ERROR - Chrome driver creation failed for test_user_0 (attempt 2): you cannot reuse the ChromeOptions object
2025-05-30 17:07:12,591 - INFO - Creating Chrome driver for test_user_0 (attempt 3/3)
2025-05-30 17:07:13,591 - INFO - Attempting webdriver-manager approach for test_user_0
2025-05-30 17:07:15,057 - INFO - ensuring close
2025-05-30 17:07:15,057 - INFO - ensuring close
2025-05-30 17:07:15,057 - INFO - ensuring close
2025-05-30 17:07:15,062 - INFO - ensuring close
2025-05-30 17:07:15,063 - INFO - ensuring close
2025-05-30 17:07:15,063 - INFO - ensuring close
2025-05-30 17:07:15,063 - INFO - ensuring close
2025-05-30 17:07:15,063 - INFO - ensuring close
2025-05-30 17:07:15,064 - INFO - ensuring close
2025-05-30 17:07:15,064 - INFO - ensuring close
2025-05-30 17:08:43,559 - INFO - Loaded 5 live feed events from file
2025-05-30 17:08:45,084 - INFO - Portable Chromium available: C:\Users\<USER>\PycharmProjects\sorceriosetup\portable_chromium\browser\chrome-win\chrome.exe
2025-05-30 17:08:45,085 - INFO - Creating Chrome driver for test_user (attempt 1/3)
2025-05-30 17:08:46,086 - INFO - Attempting webdriver-manager approach for test_user
2025-05-30 17:08:46,086 - INFO - ====== WebDriver manager ======
2025-05-30 17:08:47,629 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:08:47,739 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:08:47,859 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.55\chromedriver-win32/chromedriver.exe] found in cache
2025-05-30 17:08:49,607 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:08:54,166 - WARNING - webdriver-manager approach failed: Message: session not created: cannot connect to chrome at 127.0.0.1:53042
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0xe4f173+62931]
	GetHandleVerifier [0x0xe4f1b4+62996]
	(No symbol) [0x0xc61053]
	(No symbol) [0x0xc9ad1b]
	(No symbol) [0x0xc99d39]
	(No symbol) [0x0xc9043f]
	(No symbol) [0x0xc90276]
	(No symbol) [0x0xcda37a]
	(No symbol) [0x0xcd9c1a]
	(No symbol) [0x0xcce2a6]
	(No symbol) [0x0xc9d5f0]
	(No symbol) [0x0xc9e464]
	GetHandleVerifier [0x0x10a3463+2504899]
	GetHandleVerifier [0x0x109e892+2485490]
	GetHandleVerifier [0x0xe7590a+220522]
	GetHandleVerifier [0x0xe66388+157672]
	GetHandleVerifier [0x0xe6cb6d+184269]
	GetHandleVerifier [0x0xe57158+95672]
	GetHandleVerifier [0x0xe57300+96096]
	GetHandleVerifier [0x0xe421aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:08:54,168 - INFO - Attempting portable ChromeDriver approach for test_user
2025-05-30 17:08:56,426 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:09:00,934 - WARNING - Portable ChromeDriver approach failed: Message: session not created: cannot connect to chrome at 127.0.0.1:53061
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0xfdf173+62931]
	GetHandleVerifier [0x0xfdf1b4+62996]
	(No symbol) [0x0xdf1053]
	(No symbol) [0x0xe2ad1b]
	(No symbol) [0x0xe29d39]
	(No symbol) [0x0xe2043f]
	(No symbol) [0x0xe20276]
	(No symbol) [0x0xe6a37a]
	(No symbol) [0x0xe69c1a]
	(No symbol) [0x0xe5e2a6]
	(No symbol) [0x0xe2d5f0]
	(No symbol) [0x0xe2e464]
	GetHandleVerifier [0x0x1233463+2504899]
	GetHandleVerifier [0x0x122e892+2485490]
	GetHandleVerifier [0x0x100590a+220522]
	GetHandleVerifier [0x0xff6388+157672]
	GetHandleVerifier [0x0xffcb6d+184269]
	GetHandleVerifier [0x0xfe7158+95672]
	GetHandleVerifier [0x0xfe7300+96096]
	GetHandleVerifier [0x0xfd21aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:09:00,936 - INFO - Attempting undetected-chromedriver auto approach for test_user
2025-05-30 17:09:02,733 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:09:07,276 - WARNING - undetected-chromedriver auto approach failed: Message: session not created: cannot connect to chrome at 127.0.0.1:53079
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0x138f173+62931]
	GetHandleVerifier [0x0x138f1b4+62996]
	(No symbol) [0x0x11a1053]
	(No symbol) [0x0x11dad1b]
	(No symbol) [0x0x11d9d39]
	(No symbol) [0x0x11d043f]
	(No symbol) [0x0x11d0276]
	(No symbol) [0x0x121a37a]
	(No symbol) [0x0x1219c1a]
	(No symbol) [0x0x120e2a6]
	(No symbol) [0x0x11dd5f0]
	(No symbol) [0x0x11de464]
	GetHandleVerifier [0x0x15e3463+2504899]
	GetHandleVerifier [0x0x15de892+2485490]
	GetHandleVerifier [0x0x13b590a+220522]
	GetHandleVerifier [0x0x13a6388+157672]
	GetHandleVerifier [0x0x13acb6d+184269]
	GetHandleVerifier [0x0x1397158+95672]
	GetHandleVerifier [0x0x1397300+96096]
	GetHandleVerifier [0x0x13821aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:09:07,278 - ERROR - Chrome driver creation failed for test_user (attempt 1): Message: session not created: cannot connect to chrome at 127.0.0.1:53079
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0x138f173+62931]
	GetHandleVerifier [0x0x138f1b4+62996]
	(No symbol) [0x0x11a1053]
	(No symbol) [0x0x11dad1b]
	(No symbol) [0x0x11d9d39]
	(No symbol) [0x0x11d043f]
	(No symbol) [0x0x11d0276]
	(No symbol) [0x0x121a37a]
	(No symbol) [0x0x1219c1a]
	(No symbol) [0x0x120e2a6]
	(No symbol) [0x0x11dd5f0]
	(No symbol) [0x0x11de464]
	GetHandleVerifier [0x0x15e3463+2504899]
	GetHandleVerifier [0x0x15de892+2485490]
	GetHandleVerifier [0x0x13b590a+220522]
	GetHandleVerifier [0x0x13a6388+157672]
	GetHandleVerifier [0x0x13acb6d+184269]
	GetHandleVerifier [0x0x1397158+95672]
	GetHandleVerifier [0x0x1397300+96096]
	GetHandleVerifier [0x0x13821aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:09:09,347 - INFO - Creating Chrome driver for test_user (attempt 2/3)
2025-05-30 17:09:10,349 - INFO - Attempting webdriver-manager approach for test_user
2025-05-30 17:09:10,349 - INFO - ====== WebDriver manager ======
2025-05-30 17:09:11,816 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:09:11,927 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:09:12,037 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.55\chromedriver-win32/chromedriver.exe] found in cache
2025-05-30 17:09:13,854 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:09:18,373 - WARNING - webdriver-manager approach failed: Message: session not created: cannot connect to chrome at 127.0.0.1:53101
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0x2bf173+62931]
	GetHandleVerifier [0x0x2bf1b4+62996]
	(No symbol) [0x0xd1053]
	(No symbol) [0x0x10ad1b]
	(No symbol) [0x0x109d39]
	(No symbol) [0x0x10043f]
	(No symbol) [0x0x100276]
	(No symbol) [0x0x14a37a]
	(No symbol) [0x0x149c1a]
	(No symbol) [0x0x13e2a6]
	(No symbol) [0x0x10d5f0]
	(No symbol) [0x0x10e464]
	GetHandleVerifier [0x0x513463+2504899]
	GetHandleVerifier [0x0x50e892+2485490]
	GetHandleVerifier [0x0x2e590a+220522]
	GetHandleVerifier [0x0x2d6388+157672]
	GetHandleVerifier [0x0x2dcb6d+184269]
	GetHandleVerifier [0x0x2c7158+95672]
	GetHandleVerifier [0x0x2c7300+96096]
	GetHandleVerifier [0x0x2b21aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]
	(No symbol) [0x0]

2025-05-30 17:09:18,375 - INFO - Attempting portable ChromeDriver approach for test_user
2025-05-30 17:09:20,150 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:09:24,728 - WARNING - Portable ChromeDriver approach failed: Message: session not created: cannot connect to chrome at 127.0.0.1:53121
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0x130f173+62931]
	GetHandleVerifier [0x0x130f1b4+62996]
	(No symbol) [0x0x1121053]
	(No symbol) [0x0x115ad1b]
	(No symbol) [0x0x1159d39]
	(No symbol) [0x0x115043f]
	(No symbol) [0x0x1150276]
	(No symbol) [0x0x119a37a]
	(No symbol) [0x0x1199c1a]
	(No symbol) [0x0x118e2a6]
	(No symbol) [0x0x115d5f0]
	(No symbol) [0x0x115e464]
	GetHandleVerifier [0x0x1563463+2504899]
	GetHandleVerifier [0x0x155e892+2485490]
	GetHandleVerifier [0x0x133590a+220522]
	GetHandleVerifier [0x0x1326388+157672]
	GetHandleVerifier [0x0x132cb6d+184269]
	GetHandleVerifier [0x0x1317158+95672]
	GetHandleVerifier [0x0x1317300+96096]
	GetHandleVerifier [0x0x13021aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:09:24,730 - INFO - Attempting undetected-chromedriver auto approach for test_user
2025-05-30 17:09:26,658 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:09:31,247 - WARNING - undetected-chromedriver auto approach failed: Message: session not created: cannot connect to chrome at 127.0.0.1:53140
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0x11ef173+62931]
	GetHandleVerifier [0x0x11ef1b4+62996]
	(No symbol) [0x0x1001053]
	(No symbol) [0x0x103ad1b]
	(No symbol) [0x0x1039d39]
	(No symbol) [0x0x103043f]
	(No symbol) [0x0x1030276]
	(No symbol) [0x0x107a37a]
	(No symbol) [0x0x1079c1a]
	(No symbol) [0x0x106e2a6]
	(No symbol) [0x0x103d5f0]
	(No symbol) [0x0x103e464]
	GetHandleVerifier [0x0x1443463+2504899]
	GetHandleVerifier [0x0x143e892+2485490]
	GetHandleVerifier [0x0x121590a+220522]
	GetHandleVerifier [0x0x1206388+157672]
	GetHandleVerifier [0x0x120cb6d+184269]
	GetHandleVerifier [0x0x11f7158+95672]
	GetHandleVerifier [0x0x11f7300+96096]
	GetHandleVerifier [0x0x11e21aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:09:31,248 - ERROR - Chrome driver creation failed for test_user (attempt 2): Message: session not created: cannot connect to chrome at 127.0.0.1:53140
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0x11ef173+62931]
	GetHandleVerifier [0x0x11ef1b4+62996]
	(No symbol) [0x0x1001053]
	(No symbol) [0x0x103ad1b]
	(No symbol) [0x0x1039d39]
	(No symbol) [0x0x103043f]
	(No symbol) [0x0x1030276]
	(No symbol) [0x0x107a37a]
	(No symbol) [0x0x1079c1a]
	(No symbol) [0x0x106e2a6]
	(No symbol) [0x0x103d5f0]
	(No symbol) [0x0x103e464]
	GetHandleVerifier [0x0x1443463+2504899]
	GetHandleVerifier [0x0x143e892+2485490]
	GetHandleVerifier [0x0x121590a+220522]
	GetHandleVerifier [0x0x1206388+157672]
	GetHandleVerifier [0x0x120cb6d+184269]
	GetHandleVerifier [0x0x11f7158+95672]
	GetHandleVerifier [0x0x11f7300+96096]
	GetHandleVerifier [0x0x11e21aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:09:35,711 - INFO - Creating Chrome driver for test_user (attempt 3/3)
2025-05-30 17:09:36,712 - INFO - Attempting webdriver-manager approach for test_user
2025-05-30 17:09:36,712 - INFO - ====== WebDriver manager ======
2025-05-30 17:09:38,195 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:09:38,316 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:09:38,433 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.55\chromedriver-win32/chromedriver.exe] found in cache
2025-05-30 17:09:40,119 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:09:44,644 - WARNING - webdriver-manager approach failed: Message: session not created: cannot connect to chrome at 127.0.0.1:53162
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0x12ff173+62931]
	GetHandleVerifier [0x0x12ff1b4+62996]
	(No symbol) [0x0x1111053]
	(No symbol) [0x0x114ad1b]
	(No symbol) [0x0x1149d39]
	(No symbol) [0x0x114043f]
	(No symbol) [0x0x1140276]
	(No symbol) [0x0x118a37a]
	(No symbol) [0x0x1189c1a]
	(No symbol) [0x0x117e2a6]
	(No symbol) [0x0x114d5f0]
	(No symbol) [0x0x114e464]
	GetHandleVerifier [0x0x1553463+2504899]
	GetHandleVerifier [0x0x154e892+2485490]
	GetHandleVerifier [0x0x132590a+220522]
	GetHandleVerifier [0x0x1316388+157672]
	GetHandleVerifier [0x0x131cb6d+184269]
	GetHandleVerifier [0x0x1307158+95672]
	GetHandleVerifier [0x0x1307300+96096]
	GetHandleVerifier [0x0x12f21aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:09:44,646 - INFO - Attempting portable ChromeDriver approach for test_user
2025-05-30 17:09:46,464 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:09:51,003 - WARNING - Portable ChromeDriver approach failed: Message: session not created: cannot connect to chrome at 127.0.0.1:53181
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0xb0f173+62931]
	GetHandleVerifier [0x0xb0f1b4+62996]
	(No symbol) [0x0x921053]
	(No symbol) [0x0x95ad1b]
	(No symbol) [0x0x959d39]
	(No symbol) [0x0x95043f]
	(No symbol) [0x0x950276]
	(No symbol) [0x0x99a37a]
	(No symbol) [0x0x999c1a]
	(No symbol) [0x0x98e2a6]
	(No symbol) [0x0x95d5f0]
	(No symbol) [0x0x95e464]
	GetHandleVerifier [0x0xd63463+2504899]
	GetHandleVerifier [0x0xd5e892+2485490]
	GetHandleVerifier [0x0xb3590a+220522]
	GetHandleVerifier [0x0xb26388+157672]
	GetHandleVerifier [0x0xb2cb6d+184269]
	GetHandleVerifier [0x0xb17158+95672]
	GetHandleVerifier [0x0xb17300+96096]
	GetHandleVerifier [0x0xb021aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:09:51,005 - INFO - Attempting undetected-chromedriver auto approach for test_user
2025-05-30 17:09:52,798 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:09:57,306 - WARNING - undetected-chromedriver auto approach failed: Message: session not created: cannot connect to chrome at 127.0.0.1:53199
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0x107f173+62931]
	GetHandleVerifier [0x0x107f1b4+62996]
	(No symbol) [0x0xe91053]
	(No symbol) [0x0xecad1b]
	(No symbol) [0x0xec9d39]
	(No symbol) [0x0xec043f]
	(No symbol) [0x0xec0276]
	(No symbol) [0x0xf0a37a]
	(No symbol) [0x0xf09c1a]
	(No symbol) [0x0xefe2a6]
	(No symbol) [0x0xecd5f0]
	(No symbol) [0x0xece464]
	GetHandleVerifier [0x0x12d3463+2504899]
	GetHandleVerifier [0x0x12ce892+2485490]
	GetHandleVerifier [0x0x10a590a+220522]
	GetHandleVerifier [0x0x1096388+157672]
	GetHandleVerifier [0x0x109cb6d+184269]
	GetHandleVerifier [0x0x1087158+95672]
	GetHandleVerifier [0x0x1087300+96096]
	GetHandleVerifier [0x0x10721aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:09:57,307 - ERROR - Chrome driver creation failed for test_user (attempt 3): Message: session not created: cannot connect to chrome at 127.0.0.1:53199
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0x107f173+62931]
	GetHandleVerifier [0x0x107f1b4+62996]
	(No symbol) [0x0xe91053]
	(No symbol) [0x0xecad1b]
	(No symbol) [0x0xec9d39]
	(No symbol) [0x0xec043f]
	(No symbol) [0x0xec0276]
	(No symbol) [0x0xf0a37a]
	(No symbol) [0x0xf09c1a]
	(No symbol) [0x0xefe2a6]
	(No symbol) [0x0xecd5f0]
	(No symbol) [0x0xece464]
	GetHandleVerifier [0x0x12d3463+2504899]
	GetHandleVerifier [0x0x12ce892+2485490]
	GetHandleVerifier [0x0x10a590a+220522]
	GetHandleVerifier [0x0x1096388+157672]
	GetHandleVerifier [0x0x109cb6d+184269]
	GetHandleVerifier [0x0x1087158+95672]
	GetHandleVerifier [0x0x1087300+96096]
	GetHandleVerifier [0x0x10721aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:09:57,308 - INFO - Trying final fallback with minimal options for test_user
2025-05-30 17:09:59,151 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:10:03,972 - ERROR - All Chrome driver creation attempts failed for test_user: Message: session not created: cannot connect to chrome at 127.0.0.1:53218
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0x110f173+62931]
	GetHandleVerifier [0x0x110f1b4+62996]
	(No symbol) [0x0xf21053]
	(No symbol) [0x0xf5ad1b]
	(No symbol) [0x0xf59d39]
	(No symbol) [0x0xf5043f]
	(No symbol) [0x0xf50276]
	(No symbol) [0x0xf9a37a]
	(No symbol) [0x0xf99c1a]
	(No symbol) [0x0xf8e2a6]
	(No symbol) [0x0xf5d5f0]
	(No symbol) [0x0xf5e464]
	GetHandleVerifier [0x0x1363463+2504899]
	GetHandleVerifier [0x0x135e892+2485490]
	GetHandleVerifier [0x0x113590a+220522]
	GetHandleVerifier [0x0x1126388+157672]
	GetHandleVerifier [0x0x112cb6d+184269]
	GetHandleVerifier [0x0x1117158+95672]
	GetHandleVerifier [0x0x1117300+96096]
	GetHandleVerifier [0x0x11021aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:10:03,973 - ERROR - ChromeDriver test failed: Message: session not created: cannot connect to chrome at 127.0.0.1:53218
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0x110f173+62931]
	GetHandleVerifier [0x0x110f1b4+62996]
	(No symbol) [0x0xf21053]
	(No symbol) [0x0xf5ad1b]
	(No symbol) [0x0xf59d39]
	(No symbol) [0x0xf5043f]
	(No symbol) [0x0xf50276]
	(No symbol) [0x0xf9a37a]
	(No symbol) [0x0xf99c1a]
	(No symbol) [0x0xf8e2a6]
	(No symbol) [0x0xf5d5f0]
	(No symbol) [0x0xf5e464]
	GetHandleVerifier [0x0x1363463+2504899]
	GetHandleVerifier [0x0x135e892+2485490]
	GetHandleVerifier [0x0x113590a+220522]
	GetHandleVerifier [0x0x1126388+157672]
	GetHandleVerifier [0x0x112cb6d+184269]
	GetHandleVerifier [0x0x1117158+95672]
	GetHandleVerifier [0x0x1117300+96096]
	GetHandleVerifier [0x0x11021aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\sorceriosetup\chrome_driver_manager.py", line 130, in create_driver
    driver = uc.Chrome(service=service, options=fresh_options)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\undetected_chromedriver\__init__.py", line 466, in __init__
    super(Chrome, self).__init__(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\chrome\webdriver.py", line 45, in __init__
    super().__init__(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\chromium\webdriver.py", line 67, in __init__
    super().__init__(command_executor=executor, options=options)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\remote\webdriver.py", line 260, in __init__
    self.start_session(capabilities)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\undetected_chromedriver\__init__.py", line 724, in start_session
    super(selenium.webdriver.chrome.webdriver.WebDriver, self).start_session(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\remote\webdriver.py", line 357, in start_session
    response = self.execute(Command.NEW_SESSION, caps)["value"]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\remote\webdriver.py", line 448, in execute
    self.error_handler.check_response(response)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\remote\errorhandler.py", line 232, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: cannot connect to chrome at 127.0.0.1:53162
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0x12ff173+62931]
	GetHandleVerifier [0x0x12ff1b4+62996]
	(No symbol) [0x0x1111053]
	(No symbol) [0x0x114ad1b]
	(No symbol) [0x0x1149d39]
	(No symbol) [0x0x114043f]
	(No symbol) [0x0x1140276]
	(No symbol) [0x0x118a37a]
	(No symbol) [0x0x1189c1a]
	(No symbol) [0x0x117e2a6]
	(No symbol) [0x0x114d5f0]
	(No symbol) [0x0x114e464]
	GetHandleVerifier [0x0x1553463+2504899]
	GetHandleVerifier [0x0x154e892+2485490]
	GetHandleVerifier [0x0x132590a+220522]
	GetHandleVerifier [0x0x1316388+157672]
	GetHandleVerifier [0x0x131cb6d+184269]
	GetHandleVerifier [0x0x1307158+95672]
	GetHandleVerifier [0x0x1307300+96096]
	GetHandleVerifier [0x0x12f21aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\sorceriosetup\chrome_driver_manager.py", line 158, in create_driver
    raise uc_error
  File "C:\Users\<USER>\PycharmProjects\sorceriosetup\chrome_driver_manager.py", line 153, in create_driver
    driver = uc.Chrome(options=fresh_options3, version_main=None)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\undetected_chromedriver\__init__.py", line 466, in __init__
    super(Chrome, self).__init__(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\chrome\webdriver.py", line 45, in __init__
    super().__init__(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\chromium\webdriver.py", line 67, in __init__
    super().__init__(command_executor=executor, options=options)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\remote\webdriver.py", line 260, in __init__
    self.start_session(capabilities)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\undetected_chromedriver\__init__.py", line 724, in start_session
    super(selenium.webdriver.chrome.webdriver.WebDriver, self).start_session(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\remote\webdriver.py", line 357, in start_session
    response = self.execute(Command.NEW_SESSION, caps)["value"]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\remote\webdriver.py", line 448, in execute
    self.error_handler.check_response(response)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\remote\errorhandler.py", line 232, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: cannot connect to chrome at 127.0.0.1:53199
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0x107f173+62931]
	GetHandleVerifier [0x0x107f1b4+62996]
	(No symbol) [0x0xe91053]
	(No symbol) [0x0xecad1b]
	(No symbol) [0x0xec9d39]
	(No symbol) [0x0xec043f]
	(No symbol) [0x0xec0276]
	(No symbol) [0x0xf0a37a]
	(No symbol) [0x0xf09c1a]
	(No symbol) [0x0xefe2a6]
	(No symbol) [0x0xecd5f0]
	(No symbol) [0x0xece464]
	GetHandleVerifier [0x0x12d3463+2504899]
	GetHandleVerifier [0x0x12ce892+2485490]
	GetHandleVerifier [0x0x10a590a+220522]
	GetHandleVerifier [0x0x1096388+157672]
	GetHandleVerifier [0x0x109cb6d+184269]
	GetHandleVerifier [0x0x1087158+95672]
	GetHandleVerifier [0x0x1087300+96096]
	GetHandleVerifier [0x0x10721aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\sorceriosetup\test_chrome_driver.py", line 46, in test_chrome_driver_creation
    driver = create_chrome_driver_for_account(
  File "C:\Users\<USER>\PycharmProjects\sorceriosetup\chrome_driver_manager.py", line 206, in create_chrome_driver_for_account
    return chrome_manager.create_driver(
  File "C:\Users\<USER>\PycharmProjects\sorceriosetup\chrome_driver_manager.py", line 181, in create_driver
    raise e3
  File "C:\Users\<USER>\PycharmProjects\sorceriosetup\chrome_driver_manager.py", line 176, in create_driver
    driver = uc.Chrome(options=minimal_options)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\undetected_chromedriver\__init__.py", line 466, in __init__
    super(Chrome, self).__init__(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\chrome\webdriver.py", line 45, in __init__
    super().__init__(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\chromium\webdriver.py", line 67, in __init__
    super().__init__(command_executor=executor, options=options)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\remote\webdriver.py", line 260, in __init__
    self.start_session(capabilities)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\undetected_chromedriver\__init__.py", line 724, in start_session
    super(selenium.webdriver.chrome.webdriver.WebDriver, self).start_session(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\remote\webdriver.py", line 357, in start_session
    response = self.execute(Command.NEW_SESSION, caps)["value"]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\remote\webdriver.py", line 448, in execute
    self.error_handler.check_response(response)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\remote\errorhandler.py", line 232, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: cannot connect to chrome at 127.0.0.1:53218
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0x110f173+62931]
	GetHandleVerifier [0x0x110f1b4+62996]
	(No symbol) [0x0xf21053]
	(No symbol) [0x0xf5ad1b]
	(No symbol) [0x0xf59d39]
	(No symbol) [0x0xf5043f]
	(No symbol) [0x0xf50276]
	(No symbol) [0x0xf9a37a]
	(No symbol) [0x0xf99c1a]
	(No symbol) [0x0xf8e2a6]
	(No symbol) [0x0xf5d5f0]
	(No symbol) [0x0xf5e464]
	GetHandleVerifier [0x0x1363463+2504899]
	GetHandleVerifier [0x0x135e892+2485490]
	GetHandleVerifier [0x0x113590a+220522]
	GetHandleVerifier [0x0x1126388+157672]
	GetHandleVerifier [0x0x112cb6d+184269]
	GetHandleVerifier [0x0x1117158+95672]
	GetHandleVerifier [0x0x1117300+96096]
	GetHandleVerifier [0x0x11021aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:10:04,060 - INFO - Creating Chrome driver for test_user_0 (attempt 1/3)
2025-05-30 17:10:05,063 - INFO - Attempting webdriver-manager approach for test_user_0
2025-05-30 17:10:05,063 - INFO - ====== WebDriver manager ======
2025-05-30 17:10:06,545 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:10:06,659 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:10:06,773 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.55\chromedriver-win32/chromedriver.exe] found in cache
2025-05-30 17:10:08,432 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:10:12,922 - WARNING - webdriver-manager approach failed: Message: session not created: cannot connect to chrome at 127.0.0.1:53240
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0xd9f173+62931]
	GetHandleVerifier [0x0xd9f1b4+62996]
	(No symbol) [0x0xbb1053]
	(No symbol) [0x0xbead1b]
	(No symbol) [0x0xbe9d39]
	(No symbol) [0x0xbe043f]
	(No symbol) [0x0xbe0276]
	(No symbol) [0x0xc2a37a]
	(No symbol) [0x0xc29c1a]
	(No symbol) [0x0xc1e2a6]
	(No symbol) [0x0xbed5f0]
	(No symbol) [0x0xbee464]
	GetHandleVerifier [0x0xff3463+2504899]
	GetHandleVerifier [0x0xfee892+2485490]
	GetHandleVerifier [0x0xdc590a+220522]
	GetHandleVerifier [0x0xdb6388+157672]
	GetHandleVerifier [0x0xdbcb6d+184269]
	GetHandleVerifier [0x0xda7158+95672]
	GetHandleVerifier [0x0xda7300+96096]
	GetHandleVerifier [0x0xd921aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:10:12,923 - INFO - Attempting portable ChromeDriver approach for test_user_0
2025-05-30 17:10:14,634 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:10:19,162 - WARNING - Portable ChromeDriver approach failed: Message: session not created: cannot connect to chrome at 127.0.0.1:53259
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0x135f173+62931]
	GetHandleVerifier [0x0x135f1b4+62996]
	(No symbol) [0x0x1171053]
	(No symbol) [0x0x11aad1b]
	(No symbol) [0x0x11a9d39]
	(No symbol) [0x0x11a043f]
	(No symbol) [0x0x11a0276]
	(No symbol) [0x0x11ea37a]
	(No symbol) [0x0x11e9c1a]
	(No symbol) [0x0x11de2a6]
	(No symbol) [0x0x11ad5f0]
	(No symbol) [0x0x11ae464]
	GetHandleVerifier [0x0x15b3463+2504899]
	GetHandleVerifier [0x0x15ae892+2485490]
	GetHandleVerifier [0x0x138590a+220522]
	GetHandleVerifier [0x0x1376388+157672]
	GetHandleVerifier [0x0x137cb6d+184269]
	GetHandleVerifier [0x0x1367158+95672]
	GetHandleVerifier [0x0x1367300+96096]
	GetHandleVerifier [0x0x13521aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:10:19,164 - INFO - Attempting undetected-chromedriver auto approach for test_user_0
2025-05-30 17:10:21,224 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:10:25,745 - WARNING - undetected-chromedriver auto approach failed: Message: session not created: cannot connect to chrome at 127.0.0.1:53278
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0xcef173+62931]
	GetHandleVerifier [0x0xcef1b4+62996]
	(No symbol) [0x0xb01053]
	(No symbol) [0x0xb3ad1b]
	(No symbol) [0x0xb39d39]
	(No symbol) [0x0xb3043f]
	(No symbol) [0x0xb30276]
	(No symbol) [0x0xb7a37a]
	(No symbol) [0x0xb79c1a]
	(No symbol) [0x0xb6e2a6]
	(No symbol) [0x0xb3d5f0]
	(No symbol) [0x0xb3e464]
	GetHandleVerifier [0x0xf43463+2504899]
	GetHandleVerifier [0x0xf3e892+2485490]
	GetHandleVerifier [0x0xd1590a+220522]
	GetHandleVerifier [0x0xd06388+157672]
	GetHandleVerifier [0x0xd0cb6d+184269]
	GetHandleVerifier [0x0xcf7158+95672]
	GetHandleVerifier [0x0xcf7300+96096]
	GetHandleVerifier [0x0xce21aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:10:25,747 - ERROR - Chrome driver creation failed for test_user_0 (attempt 1): Message: session not created: cannot connect to chrome at 127.0.0.1:53278
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0xcef173+62931]
	GetHandleVerifier [0x0xcef1b4+62996]
	(No symbol) [0x0xb01053]
	(No symbol) [0x0xb3ad1b]
	(No symbol) [0x0xb39d39]
	(No symbol) [0x0xb3043f]
	(No symbol) [0x0xb30276]
	(No symbol) [0x0xb7a37a]
	(No symbol) [0x0xb79c1a]
	(No symbol) [0x0xb6e2a6]
	(No symbol) [0x0xb3d5f0]
	(No symbol) [0x0xb3e464]
	GetHandleVerifier [0x0xf43463+2504899]
	GetHandleVerifier [0x0xf3e892+2485490]
	GetHandleVerifier [0x0xd1590a+220522]
	GetHandleVerifier [0x0xd06388+157672]
	GetHandleVerifier [0x0xd0cb6d+184269]
	GetHandleVerifier [0x0xcf7158+95672]
	GetHandleVerifier [0x0xcf7300+96096]
	GetHandleVerifier [0x0xce21aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:10:28,087 - INFO - Creating Chrome driver for test_user_0 (attempt 2/3)
2025-05-30 17:10:29,089 - INFO - Attempting webdriver-manager approach for test_user_0
2025-05-30 17:10:29,089 - INFO - ====== WebDriver manager ======
2025-05-30 17:10:30,571 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:10:30,688 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:10:30,795 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.55\chromedriver-win32/chromedriver.exe] found in cache
2025-05-30 17:10:32,679 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:10:37,224 - WARNING - webdriver-manager approach failed: Message: session not created: cannot connect to chrome at 127.0.0.1:53300
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0x157f173+62931]
	GetHandleVerifier [0x0x157f1b4+62996]
	(No symbol) [0x0x1391053]
	(No symbol) [0x0x13cad1b]
	(No symbol) [0x0x13c9d39]
	(No symbol) [0x0x13c043f]
	(No symbol) [0x0x13c0276]
	(No symbol) [0x0x140a37a]
	(No symbol) [0x0x1409c1a]
	(No symbol) [0x0x13fe2a6]
	(No symbol) [0x0x13cd5f0]
	(No symbol) [0x0x13ce464]
	GetHandleVerifier [0x0x17d3463+2504899]
	GetHandleVerifier [0x0x17ce892+2485490]
	GetHandleVerifier [0x0x15a590a+220522]
	GetHandleVerifier [0x0x1596388+157672]
	GetHandleVerifier [0x0x159cb6d+184269]
	GetHandleVerifier [0x0x1587158+95672]
	GetHandleVerifier [0x0x1587300+96096]
	GetHandleVerifier [0x0x15721aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:10:37,225 - INFO - Attempting portable ChromeDriver approach for test_user_0
2025-05-30 17:10:39,219 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:10:43,760 - WARNING - Portable ChromeDriver approach failed: Message: session not created: cannot connect to chrome at 127.0.0.1:53319
from session not created: This version of ChromeDriver only supports Chrome version 137
Current browser version is 113.0.5620.0
Stacktrace:
	GetHandleVerifier [0x0xb0f173+62931]
	GetHandleVerifier [0x0xb0f1b4+62996]
	(No symbol) [0x0x921053]
	(No symbol) [0x0x95ad1b]
	(No symbol) [0x0x959d39]
	(No symbol) [0x0x95043f]
	(No symbol) [0x0x950276]
	(No symbol) [0x0x99a37a]
	(No symbol) [0x0x999c1a]
	(No symbol) [0x0x98e2a6]
	(No symbol) [0x0x95d5f0]
	(No symbol) [0x0x95e464]
	GetHandleVerifier [0x0xd63463+2504899]
	GetHandleVerifier [0x0xd5e892+2485490]
	GetHandleVerifier [0x0xb3590a+220522]
	GetHandleVerifier [0x0xb26388+157672]
	GetHandleVerifier [0x0xb2cb6d+184269]
	GetHandleVerifier [0x0xb17158+95672]
	GetHandleVerifier [0x0xb17300+96096]
	GetHandleVerifier [0x0xb021aa+9738]
	BaseThreadInitThunk [0x0x772505c9+25]
	RtlGetAppContainerNamedObjectPath [0x0x77437c5d+237]
	RtlGetAppContainerNamedObjectPath [0x0x77437c2d+189]

2025-05-30 17:10:43,762 - INFO - Attempting undetected-chromedriver auto approach for test_user_0
2025-05-30 17:10:45,817 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:10:48,877 - INFO - ensuring close
2025-05-30 17:10:48,878 - INFO - ensuring close
2025-05-30 17:10:48,878 - INFO - ensuring close
2025-05-30 17:10:48,879 - INFO - ensuring close
2025-05-30 17:10:48,879 - INFO - ensuring close
2025-05-30 17:10:48,879 - INFO - ensuring close
2025-05-30 17:10:48,880 - INFO - ensuring close
2025-05-30 17:10:48,880 - INFO - ensuring close
2025-05-30 17:10:48,881 - INFO - ensuring close
2025-05-30 17:10:48,881 - INFO - ensuring close
2025-05-30 17:10:48,881 - INFO - ensuring close
2025-05-30 17:10:48,882 - INFO - ensuring close
2025-05-30 17:10:48,882 - INFO - ensuring close
2025-05-30 17:10:48,883 - INFO - ensuring close
2025-05-30 17:10:48,883 - INFO - ensuring close
2025-05-30 17:10:48,883 - INFO - ensuring close
2025-05-30 17:12:31,790 - INFO - Loaded 5 live feed events from file
2025-05-30 17:12:43,365 - WARNING - Could not determine portable Chrome version, disabling portable mode
2025-05-30 17:12:43,367 - INFO - Creating Chrome driver for test_user (attempt 1/3)
2025-05-30 17:12:44,368 - INFO - Attempting system Chrome with webdriver-manager for test_user
2025-05-30 17:12:44,368 - INFO - ====== WebDriver manager ======
2025-05-30 17:12:45,860 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:12:45,977 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:12:46,091 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.55\chromedriver-win32/chromedriver.exe] found in cache
2025-05-30 17:12:47,876 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:12:50,435 - INFO - setting properties for headless
2025-05-30 17:12:50,435 - INFO - Chrome driver created with system Chrome + webdriver-manager for test_user
2025-05-30 17:13:05,367 - INFO - Creating Chrome driver for test_user_0 (attempt 1/3)
2025-05-30 17:13:06,369 - INFO - Attempting system Chrome with webdriver-manager for test_user_0
2025-05-30 17:13:06,369 - INFO - ====== WebDriver manager ======
2025-05-30 17:13:07,844 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:13:07,964 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:13:08,071 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.55\chromedriver-win32/chromedriver.exe] found in cache
2025-05-30 17:13:09,750 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:13:12,293 - INFO - setting properties for headless
2025-05-30 17:13:12,294 - INFO - Chrome driver created with system Chrome + webdriver-manager for test_user_0
2025-05-30 17:13:12,296 - INFO - Creating Chrome driver for test_user_1 (attempt 1/3)
2025-05-30 17:13:13,296 - INFO - Attempting system Chrome with webdriver-manager for test_user_1
2025-05-30 17:13:13,296 - INFO - ====== WebDriver manager ======
2025-05-30 17:13:14,804 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:13:14,922 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:13:15,038 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.55\chromedriver-win32/chromedriver.exe] found in cache
2025-05-30 17:13:15,066 - WARNING - could not detect version_main.therefore, we are assuming it is chrome 108 or higher
2025-05-30 17:13:15,708 - INFO - setting properties for headless
2025-05-30 17:13:15,708 - INFO - Chrome driver created with system Chrome + webdriver-manager for test_user_1
2025-05-30 17:13:15,709 - INFO - Creating Chrome driver for test_user_2 (attempt 1/3)
2025-05-30 17:13:16,710 - INFO - Attempting system Chrome with webdriver-manager for test_user_2
2025-05-30 17:13:16,710 - INFO - ====== WebDriver manager ======
2025-05-30 17:13:18,174 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:13:18,303 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:13:18,417 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.55\chromedriver-win32/chromedriver.exe] found in cache
2025-05-30 17:13:18,445 - WARNING - could not detect version_main.therefore, we are assuming it is chrome 108 or higher
2025-05-30 17:13:19,090 - INFO - setting properties for headless
2025-05-30 17:13:19,091 - INFO - Chrome driver created with system Chrome + webdriver-manager for test_user_2
2025-05-30 17:13:46,817 - INFO - ensuring close
2025-05-30 17:13:46,817 - INFO - ensuring close
2025-05-30 17:13:46,818 - INFO - ensuring close
2025-05-30 17:13:46,818 - INFO - ensuring close
2025-05-30 17:19:06,073 - INFO - Loaded 3 live feed events from file
2025-05-30 17:19:18,404 - WARNING - Could not determine portable Chrome version, disabling portable mode
2025-05-30 17:19:18,406 - INFO - Creating Chrome driver for test_user (attempt 1/3)
2025-05-30 17:19:19,407 - INFO - Attempting system Chrome with webdriver-manager for test_user
2025-05-30 17:19:19,407 - INFO - ====== WebDriver manager ======
2025-05-30 17:19:20,931 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:19:21,062 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:19:21,179 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.55\chromedriver-win32/chromedriver.exe] found in cache
2025-05-30 17:19:23,026 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:19:25,559 - INFO - setting properties for headless
2025-05-30 17:19:25,559 - INFO - Chrome driver created with system Chrome + webdriver-manager for test_user
2025-05-30 17:19:37,355 - INFO - Creating Chrome driver for test_user_0 (attempt 1/3)
2025-05-30 17:19:38,357 - INFO - Attempting system Chrome with webdriver-manager for test_user_0
2025-05-30 17:19:38,357 - INFO - ====== WebDriver manager ======
2025-05-30 17:19:39,820 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:19:39,939 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:19:40,051 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.55\chromedriver-win32/chromedriver.exe] found in cache
2025-05-30 17:19:43,060 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-05-30 17:19:45,536 - INFO - setting properties for headless
2025-05-30 17:19:45,536 - INFO - Chrome driver created with system Chrome + webdriver-manager for test_user_0
2025-05-30 17:19:45,538 - INFO - Creating Chrome driver for test_user_1 (attempt 1/3)
2025-05-30 17:19:46,539 - INFO - Attempting system Chrome with webdriver-manager for test_user_1
2025-05-30 17:19:46,539 - INFO - ====== WebDriver manager ======
2025-05-30 17:19:48,023 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:19:48,140 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:19:48,256 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.55\chromedriver-win32/chromedriver.exe] found in cache
2025-05-30 17:19:48,285 - WARNING - could not detect version_main.therefore, we are assuming it is chrome 108 or higher
2025-05-30 17:19:48,920 - INFO - setting properties for headless
2025-05-30 17:19:48,921 - INFO - Chrome driver created with system Chrome + webdriver-manager for test_user_1
2025-05-30 17:19:48,922 - INFO - Creating Chrome driver for test_user_2 (attempt 1/3)
2025-05-30 17:19:49,923 - INFO - Attempting system Chrome with webdriver-manager for test_user_2
2025-05-30 17:19:49,923 - INFO - ====== WebDriver manager ======
2025-05-30 17:19:51,389 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:19:51,501 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-30 17:19:51,624 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.55\chromedriver-win32/chromedriver.exe] found in cache
2025-05-30 17:19:51,652 - WARNING - could not detect version_main.therefore, we are assuming it is chrome 108 or higher
2025-05-30 17:19:52,324 - INFO - setting properties for headless
2025-05-30 17:19:52,325 - INFO - Chrome driver created with system Chrome + webdriver-manager for test_user_2
2025-05-30 17:19:56,987 - INFO - ensuring close
2025-05-30 17:19:56,987 - INFO - ensuring close
2025-05-30 17:19:56,988 - INFO - ensuring close
2025-05-30 17:19:56,988 - INFO - ensuring close
