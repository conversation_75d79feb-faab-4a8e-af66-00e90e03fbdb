"""
Centralized ChromeDriver management for Sorcerio project.
Provides automatic version compatibility across different Chrome installations.
"""

import logging
import os
import threading
import time
import undetected_chromedriver as uc
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager as WDM_ChromeDriverManager

# Try to import ChromeType, but don't fail if it's not available
try:
    from webdriver_manager.core.os_manager import ChromeType
    CHROME_TYPE_AVAILABLE = True
except ImportError:
    CHROME_TYPE_AVAILABLE = False
    logging.warning("ChromeType not available in this webdriver-manager version")


class SorcerioDriverManager:
    """
    Manages ChromeDriver creation with automatic version compatibility.
    Handles portable Chromium integration and provides comprehensive fallback mechanisms.
    """
    
    def __init__(self):
        self._lock = threading.Lock()
        self._portable_available = False
        self._portable_binary_path = None
        self._portable_driver_path = None
        self._check_portable_chromium()
    
    def _check_portable_chromium(self):
        """Check if portable Chromium is available and cache paths."""
        try:
            from download import setup_portable_chromium
            self._portable_binary_path, self._portable_driver_path = setup_portable_chromium()

            # Check Chrome version compatibility
            chrome_version = self._get_chrome_version(self._portable_binary_path)
            if chrome_version:
                logging.info(f"Portable Chromium available: {self._portable_binary_path} (version {chrome_version})")
                # Only use portable if it's a reasonably recent version
                if chrome_version.startswith(('113', '114', '115', '116', '117', '118', '119', '120')):
                    logging.warning(f"Portable Chrome version {chrome_version} is quite old, may have compatibility issues")
                self._portable_available = True
            else:
                logging.warning("Could not determine portable Chrome version, disabling portable mode")
                self._portable_available = False
        except Exception as e:
            logging.info(f"Portable Chromium not available: {e}")
            self._portable_available = False

    def _get_chrome_version(self, chrome_path):
        """Get Chrome version from binary."""
        try:
            import subprocess
            result = subprocess.run([chrome_path, '--version'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                # Extract version number from output like "Google Chrome 113.0.5620.0"
                version_line = result.stdout.strip()
                import re
                match = re.search(r'(\d+\.\d+\.\d+\.\d+)', version_line)
                if match:
                    return match.group(1)
        except Exception as e:
            logging.debug(f"Could not get Chrome version: {e}")
        return None
    
    def create_chrome_options(self, profile_path=None, headless=True, additional_args=None, use_portable=None):
        """
        Create standardized Chrome options.
        NOTE: Creates a fresh ChromeOptions object each time to avoid reuse issues.

        Args:
            profile_path (str, optional): Path to Chrome profile directory
            headless (bool): Whether to run in headless mode
            additional_args (list, optional): Additional Chrome arguments
            use_portable (bool, optional): Force use/non-use of portable Chrome

        Returns:
            uc.ChromeOptions: Configured Chrome options
        """
        # Always create a fresh ChromeOptions object to avoid reuse issues
        options = uc.ChromeOptions()

        # Set binary location based on preference
        if use_portable is None:
            use_portable = self._portable_available

        if use_portable and self._portable_available:
            options.binary_location = self._portable_binary_path

        # Profile settings
        if profile_path:
            options.add_argument(f'--user-data-dir={profile_path}')

        # Basic arguments
        if headless:
            options.add_argument('--headless=new')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-notifications')
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--remote-debugging-port=0')
        options.add_argument('--lang=en-US')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) ' +
                           'AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36')

        # Additional arguments
        if additional_args:
            for arg in additional_args:
                options.add_argument(arg)

        # Preferences
        prefs = {
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 2
        }
        options.add_experimental_option("prefs", prefs)

        return options
    
    def create_driver(self, account_name="default", profile_path=None, headless=True, 
                     additional_args=None, max_retries=3):
        """
        Create a Chrome driver with automatic version compatibility.
        
        Args:
            account_name (str): Name for logging purposes
            profile_path (str, optional): Path to Chrome profile directory
            headless (bool): Whether to run in headless mode
            additional_args (list, optional): Additional Chrome arguments
            max_retries (int): Maximum number of retry attempts
            
        Returns:
            webdriver.Chrome: Configured Chrome driver instance
            
        Raises:
            Exception: If all driver creation attempts fail
        """
        options = self.create_chrome_options(profile_path, headless, additional_args)
        
        for attempt in range(max_retries):
            try:
                logging.info(f"Creating Chrome driver for {account_name} (attempt {attempt + 1}/{max_retries})")

                with self._lock:
                    # Add delay to prevent race conditions
                    time.sleep(1.0)

                    # Create fresh options for each attempt to avoid reuse issues
                    fresh_options = self.create_chrome_options(profile_path, headless, additional_args)

                    # Strategy 1: Use system Chrome with webdriver-manager (most reliable for latest versions)
                    try:
                        logging.info(f"Attempting system Chrome with webdriver-manager for {account_name}")
                        fresh_options_system = self.create_chrome_options(profile_path, headless, additional_args, use_portable=False)
                        service = Service(WDM_ChromeDriverManager().install())
                        driver = uc.Chrome(service=service, options=fresh_options_system)
                        logging.info(f"Chrome driver created with system Chrome + webdriver-manager for {account_name}")
                        return driver
                    except Exception as system_error:
                        logging.warning(f"System Chrome + webdriver-manager approach failed: {system_error}")

                    # Strategy 2: Use portable ChromeDriver if available (for older Chrome versions)
                    if self._portable_available:
                        try:
                            logging.info(f"Attempting portable ChromeDriver approach for {account_name}")
                            fresh_options2 = self.create_chrome_options(profile_path, headless, additional_args, use_portable=True)
                            service = Service(self._portable_driver_path)
                            driver = uc.Chrome(service=service, options=fresh_options2)
                            logging.info(f"Chrome driver created with portable ChromeDriver for {account_name}")
                            return driver
                        except Exception as portable_error:
                            logging.warning(f"Portable ChromeDriver approach failed: {portable_error}")

                    # Strategy 3: Try version-specific ChromeDriver for portable Chrome
                    if self._portable_available:
                        try:
                            logging.info(f"Attempting version-specific ChromeDriver for portable Chrome for {account_name}")
                            chrome_version = self._get_chrome_version(self._portable_binary_path)
                            if chrome_version:
                                major_version = chrome_version.split('.')[0]
                                logging.info(f"Detected Chrome major version: {major_version}")
                                fresh_options3 = self.create_chrome_options(profile_path, headless, additional_args, use_portable=True)
                                service = Service(WDM_ChromeDriverManager(version=f"{major_version}.0.0.0").install())
                                driver = uc.Chrome(service=service, options=fresh_options3)
                                logging.info(f"Chrome driver created with version-specific driver for {account_name}")
                                return driver
                        except Exception as version_error:
                            logging.warning(f"Version-specific ChromeDriver approach failed: {version_error}")

                    # Strategy 4: Let undetected-chromedriver handle everything automatically
                    try:
                        logging.info(f"Attempting undetected-chromedriver auto approach for {account_name}")
                        fresh_options4 = self.create_chrome_options(profile_path, headless, additional_args, use_portable=False)
                        driver = uc.Chrome(options=fresh_options4, version_main=None)
                        logging.info(f"Chrome driver created with undetected-chromedriver auto for {account_name}")
                        return driver
                    except Exception as uc_error:
                        logging.warning(f"undetected-chromedriver auto approach failed: {uc_error}")
                        raise uc_error
                            
            except Exception as e:
                logging.error(f"Chrome driver creation failed for {account_name} (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    # Add random delay to avoid file conflicts
                    import random
                    delay = 2 + random.uniform(0, 3)  # 2-5 seconds random delay
                    time.sleep(delay)
                else:
                    # Final fallback: try with minimal options
                    try:
                        logging.info(f"Trying final fallback with minimal options for {account_name}")
                        minimal_options = self.create_chrome_options(headless=True, additional_args=[
                            '--disable-blink-features=AutomationControlled'
                        ])

                        # Try with system Chrome and let undetected-chromedriver handle driver
                        driver = uc.Chrome(options=minimal_options)
                        logging.info(f"Chrome driver created with minimal fallback for {account_name}")
                        return driver
                    except Exception as e3:
                        logging.error(f"All Chrome driver creation attempts failed for {account_name}: {e3}")
                        raise e3


# Global instance for easy access
chrome_manager = SorcerioDriverManager()


def create_chrome_driver_for_account(account_username, profile_path=None, headless=True, additional_args=None):
    """
    Convenience function to create a Chrome driver for a specific account.
    
    Args:
        account_username (str): Username for the account
        profile_path (str, optional): Custom profile path (auto-generated if None)
        headless (bool): Whether to run in headless mode
        additional_args (list, optional): Additional Chrome arguments
        
    Returns:
        webdriver.Chrome: Configured Chrome driver instance
    """
    if profile_path is None:
        base_dir = os.path.dirname(os.path.abspath(__file__))
        profile_path = os.path.join(base_dir, f"chrome_profile_{account_username}")
        os.makedirs(profile_path, exist_ok=True)
    
    return chrome_manager.create_driver(
        account_name=account_username,
        profile_path=profile_path,
        headless=headless,
        additional_args=additional_args
    )
