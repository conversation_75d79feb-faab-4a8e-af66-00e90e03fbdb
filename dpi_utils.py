#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
DPI Duyarlılığı ve Çözünürlük Uyumluluğu Yardımcı Modülü
Sorcerio uygulamasının tüm Windows çözünürlüklerinde orantılı görünmesi için gerekli fonksiyonlar
"""

import os
import logging
from PyQt5 import QtCore, QtWidgets, QtGui


def apply_high_dpi_settings():
    """
    Uygulama başlamadan önce yüksek DPI destek ayarlarını uygular.
    Bu fonksiyon QApplication oluşturulmadan ÖNCE çağrılmalıdır.
    """
    # Qt yüksek DPI ölçeklendirmesi ve yüksek çözünürlüklü pixmap desteği
    QtCore.QCoreApplication.setAttribute(QtCore.Qt.AA_EnableHighDpiScaling, True)
    QtCore.QCoreApplication.setAttribute(QtCore.Qt.AA_UseHighDpiPixmaps, True)
    
    # DPI ölçeklendirmesinde kesirli değerleri koru (125%, 150% gibi)
    # Bu ayar Windows'ta 125% ve 150% gibi ara değerlerin doğru uygulanmasını sağlar
    os.environ["QT_SCALE_FACTOR_ROUNDING_POLICY"] = "PassThrough"
    
    # Qt 5.14+ için programatik olarak da ayarlayabiliriz
    try:
        QtGui.QGuiApplication.setHighDpiScaleFactorRoundingPolicy(
            QtCore.Qt.HighDpiScaleFactorRoundingPolicy.PassThrough
        )
    except AttributeError:
        # Eski Qt sürümlerinde bu method yoksa ortam değişkeni yeterli
        pass
    
    logging.info("Yüksek DPI ayarları uygulandı")


def get_device_pixel_ratio():
    """
    Birincil ekranın DPI ölçek oranını döndürür.
    
    Returns:
        float: DPI ölçek oranı (örn: 1.0, 1.25, 1.5, 2.0)
    """
    try:
        app = QtWidgets.QApplication.instance()
        if app is None:
            return 1.0
            
        screen = app.primaryScreen()
        if screen is None:
            return 1.0
            
        ratio = screen.devicePixelRatio()
        logging.debug(f"Cihaz piksel oranı: {ratio}")
        return ratio
    except Exception as e:
        logging.warning(f"DPI oranı alınırken hata: {e}")
        return 1.0


def get_screen_geometry():
    """
    Birincil ekranın geometri bilgilerini döndürür.
    
    Returns:
        dict: Ekran boyutları ve DPI bilgileri
    """
    try:
        app = QtWidgets.QApplication.instance()
        if app is None:
            return None
            
        screen = app.primaryScreen()
        if screen is None:
            return None
            
        geometry = screen.availableGeometry()
        physical_dpi = screen.physicalDotsPerInch()
        logical_dpi = screen.logicalDotsPerInch()
        device_ratio = screen.devicePixelRatio()
        
        info = {
            'width': geometry.width(),
            'height': geometry.height(),
            'physical_dpi': physical_dpi,
            'logical_dpi': logical_dpi,
            'device_pixel_ratio': device_ratio,
            'scale_factor': device_ratio
        }
        
        logging.info(f"Ekran bilgileri: {info}")
        return info
        
    except Exception as e:
        logging.warning(f"Ekran geometrisi alınırken hata: {e}")
        return None


def calculate_optimal_window_size(base_width=900, base_height=600, min_scale=0.7, max_scale=1.2):
    """
    Ekran boyutuna göre optimal pencere boyutunu hesaplar.
    
    Args:
        base_width (int): Temel pencere genişliği
        base_height (int): Temel pencere yüksekliği  
        min_scale (float): Minimum ölçek faktörü
        max_scale (float): Maksimum ölçek faktörü
        
    Returns:
        tuple: (width, height) optimal pencere boyutu
    """
    try:
        screen_info = get_screen_geometry()
        if not screen_info:
            return base_width, base_height
            
        available_width = screen_info['width']
        available_height = screen_info['height']
        
        # Ekranın %80'ini aşmayacak şekilde ölçekle
        max_width = available_width * 0.8
        max_height = available_height * 0.8
        
        # Ölçek faktörünü hesapla
        width_scale = max_width / base_width
        height_scale = max_height / base_height
        scale_factor = min(width_scale, height_scale)
        
        # Ölçek sınırlarını uygula
        scale_factor = max(min_scale, min(scale_factor, max_scale))
        
        optimal_width = int(base_width * scale_factor)
        optimal_height = int(base_height * scale_factor)
        
        logging.info(f"Optimal pencere boyutu: {optimal_width}x{optimal_height} (ölçek: {scale_factor:.2f})")
        return optimal_width, optimal_height
        
    except Exception as e:
        logging.warning(f"Optimal boyut hesaplanırken hata: {e}")
        return base_width, base_height


def apply_webengine_dpi_scaling(webview):
    """
    QWebEngineView için DPI ölçeklendirmesi uygular.
    
    Args:
        webview (QWebEngineView): Ölçeklenecek web görünümü
    """
    try:
        device_ratio = get_device_pixel_ratio()
        
        # Eğer DPI ölçeklendirmesi zaten Qt tarafından yapılıyorsa
        # ek zoom uygulamaya gerek olmayabilir
        if device_ratio > 1.0:
            # Sadece 1.0'dan büyük değerlerde zoom uygula
            webview.setZoomFactor(device_ratio)
            logging.info(f"WebEngine zoom faktörü ayarlandı: {device_ratio}")
        else:
            # Normal DPI'da zoom faktörünü 1.0'da bırak
            webview.setZoomFactor(1.0)
            
    except Exception as e:
        logging.warning(f"WebEngine DPI ölçeklendirmesi uygulanırken hata: {e}")


def get_font_scale_factor():
    """
    Sistem font ölçeklendirme faktörünü döndürür.
    
    Returns:
        float: Font ölçek faktörü
    """
    try:
        app = QtWidgets.QApplication.instance()
        if app is None:
            return 1.0
            
        # Sistem font boyutunu al
        font = app.font()
        point_size = font.pointSize()
        
        # Standart font boyutu (9pt) ile karşılaştır
        standard_size = 9
        scale_factor = point_size / standard_size if point_size > 0 else 1.0
        
        logging.debug(f"Font ölçek faktörü: {scale_factor}")
        return scale_factor
        
    except Exception as e:
        logging.warning(f"Font ölçek faktörü hesaplanırken hata: {e}")
        return 1.0


def log_display_info():
    """
    Ekran ve DPI bilgilerini loglara yazdırır (debug amaçlı).
    """
    try:
        screen_info = get_screen_geometry()
        device_ratio = get_device_pixel_ratio()
        font_scale = get_font_scale_factor()
        
        logging.info("=== EKRAN VE DPI BİLGİLERİ ===")
        if screen_info:
            logging.info(f"Ekran boyutu: {screen_info['width']}x{screen_info['height']}")
            logging.info(f"Fiziksel DPI: {screen_info['physical_dpi']:.1f}")
            logging.info(f"Mantıksal DPI: {screen_info['logical_dpi']:.1f}")
            logging.info(f"Cihaz piksel oranı: {screen_info['device_pixel_ratio']:.2f}")
        logging.info(f"Font ölçek faktörü: {font_scale:.2f}")
        logging.info("================================")
        
    except Exception as e:
        logging.warning(f"Ekran bilgileri loglanırken hata: {e}")


def setup_application_scaling(app):
    """
    QApplication için ölçeklendirme ayarlarını yapar.
    
    Args:
        app (QApplication): Uygulama nesnesi
    """
    try:
        # Font DPI ayarlarını kontrol et
        app.setAttribute(QtCore.Qt.AA_Use96Dpi, False)
        
        # Yüksek DPI ekranlarda font rendering'i iyileştir
        app.setAttribute(QtCore.Qt.AA_UseHighDpiPixmaps, True)
        
        logging.info("Uygulama ölçeklendirme ayarları tamamlandı")
        
    except Exception as e:
        logging.warning(f"Uygulama ölçeklendirme ayarları yapılırken hata: {e}")
